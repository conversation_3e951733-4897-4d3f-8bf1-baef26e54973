# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Diagnostic reports (https://nodejs.org/api/report.html)
.report/

# Node.js
# dependencies
/node_modules
/.pnp
.pnp.js

# production
/build
/dist
/tmp
/out-tsc
/.next
/.nuxt
/.cache
/.parcel-cache

# misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.staging.local
.env.preview.local
.env.*.local
.env
.npm
.eslintcache
.stylelintcache
.prettiercache
.yarn/
.idea/
.vscode/
*.tsbuildinfo