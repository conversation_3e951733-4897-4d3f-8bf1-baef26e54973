import Head from 'next/head';
import Header from '../components/Header';
import <PERSON> from '../components/Hero';
import KeyFeatures from '../components/KeyFeatures';
import Testimonials from '../components/Testimonials';
import HowItWorks from '../components/HowItWorks';
import Footer from '../components/Footer';

const Home = () => {
  return (
    <div className="min-h-screen bg-gray-50 text-gray-800">
      <Head>
        <title>AI Prompt Vault – $5 AI Prompt Packs to Boost Your Productivity</title>
        <meta name="description" content="Get battle-tested AI prompt frameworks for only $5. Structured Meta-Directives, Orchestrator Workflow, and more. Instant download." />
        <meta property="og:title" content="AI Prompt Vault – $5 AI Prompt Packs to Boost Your Productivity" />
        <meta property="og:description" content="Get battle-tested AI prompt frameworks for only $5. Structured Meta-Directives, Orchestrator Workflow, and more. Instant download." />
        <meta property="og:image" content="/assets/og-ai-prompt.png" />
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="AI Prompt Vault – $5 AI Prompt Packs to Boost Your Productivity" />
        <meta name="twitter:description" content="Get battle-tested AI prompt frameworks for only $5. Structured Meta-Directives, Orchestrator Workflow, and more. Instant download." />
        <meta name="twitter:image" content="/assets/og-ai-prompt.png" />
      </Head>

      <a href="#main-content" className="sr-only focus:not-sr-only">Skip to main content</a>

      <Header aria-label="Site Header" />

      <main id="main-content" className="flex flex-col items-center justify-center w-full">
        <Hero aria-label="Hero Section" />
        <KeyFeatures aria-label="Key Features Section" />
        <Testimonials aria-label="Testimonials Section" />
        <HowItWorks aria-label="How It Works Section" />
      </main>

      <Footer aria-label="Site Footer" />
    </div>
  );
};

export default Home;